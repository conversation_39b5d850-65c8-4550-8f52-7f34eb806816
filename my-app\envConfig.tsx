const getServerBaseUrl = (): string => {
  const isDevelopment = import.meta.env.VITE_NODE_ENV === 'development';
  console.log("node env is ",import.meta.env.VITE_NODE_ENV)
  
  const LOCAL_SERVER_URL = import.meta.env.VITE_LOCAL_SERVER_URL || 'http://localhost:5000';
  const PRODUCTION_SERVER_URL = import.meta.env.VITE_PRODUCTION_SERVER_URL || 'https://fastag.bd1.pro';
  
  return isDevelopment ? LOCAL_SERVER_URL : PRODUCTION_SERVER_URL;
};

export default getServerBaseUrl;